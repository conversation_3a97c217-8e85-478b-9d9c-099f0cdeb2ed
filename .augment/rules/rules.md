
You are working with a React Router v7 project that uses the following technology stack:
- **Build tool**: Vite
- **Icons**: lucide-react
- **Data fetching**: SWR
- **State management**: Zustand  
- **UI components**: Radix UI
- **Styling**: Tailwind CSS
- **SSR**: Disabled (set to false)

**Project Structure:**
- There is a `figma/` folder in the project root containing frontend code generated by Figma Make (TM)
- The figma code includes only components and styled UI for Radix primitives
- The figma code does NOT include project configuration files (no package.json, vite.config.ts, etc.)

**Important Notice**
- Whenever a file in the `figma/components/ui` folder is referenced, use the corresponding file in the `app/components/ui` folder instead
- We have already replicated the `ui` components to `app/components/ui`

**Your Tasks:**

1. **Replicate the Figma code** into the main project structure without modifying any files in the `figma/` folder

2. **Adapt the replicated code** to work with the project's dependencies and conventions:
   - Convert import statements to match the project's package versions (e.g., change `import * as AccordionPrimitive from "@radix-ui/react-accordion@1.2.3"` to `import { Accordion } from "@radix-ui/react-accordion"`)
   - Ensure compatibility with the project's Vite build configuration
   - Make any other necessary adjustments for integration

3. **Maintain synchronization** when the figma code is updated:
   - When files in the `figma/` folder are updated, update the corresponding replicated files
   - **CRITICAL**: Preserve any custom backend integration code that has been added to the replicated files - do not overwrite data fetching logic, API calls, or other backend-related modifications

**Important Constraints:**
- Never modify files in the `figma/` folder
- When updating replicated code, carefully preserve existing backend integration logic
- Ensure all changes maintain compatibility with the project's technology stack