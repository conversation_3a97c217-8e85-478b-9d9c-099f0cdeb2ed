import React, { useState } from 'react';
import { Plus, Edit, Trash2, Users, Shield, Search, X } from 'lucide-react';
import useSWR from 'swr';
import { Role } from '../../types/auth';
import { api, parsePermissions } from '../../lib/api';
import { useAuthStore } from '../../store/auth-store';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Skeleton } from '../ui/skeleton';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { toast } from 'sonner@2.0.3';
import { RoleDialog } from './RoleDialog';
import { DeleteConfirmDialog } from '../shared/DeleteConfirmDialog';
import { TablePagination, calculateTotalPages, paginateData } from '../shared/TablePagination';

export function RoleManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [userCountFilter, setUserCountFilter] = useState<string>('');
  const [permissionFilter, setPermissionFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const {
    roleDialogOpen,
    setRoleDialogOpen,
    selectedRole,
    setSelectedRole,
    deleteConfirmOpen,
    setDeleteConfirmOpen,
    deleteTarget,
    setDeleteTarget
  } = useAuthStore();

  // Fetch roles with SWR
  const { data: roles, error, mutate, isLoading } = useSWR(
    'roles',
    api.getRoles,
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000
    }
  );

  // Fetch users to show role usage
  const { data: usersData } = useSWR(
    'users',
    () => api.getUsers({ page: 1, size: 1000 }),
    { revalidateOnFocus: false }
  );

  // Fetch permissions to calculate actual permission count
  const { data: allPermissions } = useSWR(
    'permissions',
    api.getPermissions,
    { revalidateOnFocus: false }
  );

  const handleCreateRole = () => {
    setSelectedRole(null);
    setRoleDialogOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setRoleDialogOpen(true);
  };

  const handleDeleteRole = (role: Role) => {
    setDeleteTarget({ type: 'role', id: role.id });
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!deleteTarget || deleteTarget.type !== 'role') return;
    
    try {
      await api.deleteRole(deleteTarget.id);
      mutate();
      toast.success('角色删除成功');
      setDeleteConfirmOpen(false);
      setDeleteTarget(null);
    } catch (error: any) {
      toast.error(error.message || '删除失败');
    }
  };

  const getRoleUserCount = (roleId: number) => {
    if (!usersData) return 0;
    return usersData.items.filter(user => 
      user.roles.some(role => role.id === roleId)
    ).length;
  };

  const getRoleUsers = (roleId: number) => {
    if (!usersData) return [];
    return usersData.items.filter(user => 
      user.roles.some(role => role.id === roleId)
    );
  };

  const getPermissionCount = (permissionString: string) => {
    if (!permissionString || !allPermissions) return 0;
    
    const permissionTokens = permissionString.split(',').map(p => p.trim()).filter(Boolean);
    let count = 0;
    
    permissionTokens.forEach(token => {
      const [type, service, module, action] = token.split(':');
      
      if (!type || !service || !module || !action) return;
      
      // Count matching permissions based on wildcards
      if (action === '*' && module === '*') {
        // service level wildcard: api:user:*:*
        count += allPermissions.filter(p => 
          p.type === type && p.service === service
        ).length;
      } else if (action === '*') {
        // module level wildcard: api:content:article:*
        count += allPermissions.filter(p => 
          p.type === type && p.service === service && p.module === module
        ).length;
      } else if (module === '*') {
        // service action wildcard: api:content:*:read
        count += allPermissions.filter(p => 
          p.type === type && p.service === service && p.action === action
        ).length;
      } else {
        // exact permission: api:user:profile:read
        const exists = allPermissions.some(p => 
          p.type === type && p.service === service && 
          p.module === module && p.action === action
        );
        if (exists) count += 1;
      }
    });
    
    return count;
  };

  // Filter and search functions
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleUserCountFilter = (value: string) => {
    setUserCountFilter(value);
  };

  const handlePermissionFilter = (value: string) => {
    setPermissionFilter(value);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setUserCountFilter('');
    setPermissionFilter('');
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // Filter roles based on search and filters
  const allFilteredRoles = roles?.filter(role => {
    const userCount = getRoleUserCount(role.id);
    const permissionCount = getPermissionCount(role.permissions);

    // Text search - check name, description, and permission content
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      const matchesName = role.name.toLowerCase().includes(search);
      const matchesDescription = role.description.toLowerCase().includes(search);
      const matchesPermissions = role.permissions.toLowerCase().includes(search);
      
      // Also search in parsed permission details
      let matchesPermissionDetails = false;
      if (allPermissions) {
        const rolePermissions = parsePermissions(role.permissions);
        matchesPermissionDetails = rolePermissions.some(perm => 
          perm.name?.toLowerCase().includes(search) ||
          perm.description?.toLowerCase().includes(search) ||
          perm.service.toLowerCase().includes(search) ||
          perm.module.toLowerCase().includes(search) ||
          perm.action.toLowerCase().includes(search)
        );
      }
      
      if (!matchesName && !matchesDescription && !matchesPermissions && !matchesPermissionDetails) {
        return false;
      }
    }

    // User count filter
    if (userCountFilter && userCountFilter !== 'all') {
      switch (userCountFilter) {
        case 'none':
          if (userCount > 0) return false;
          break;
        case 'some':
          if (userCount === 0) return false;
          break;
        case 'many':
          if (userCount < 5) return false;
          break;
      }
    }

    // Permission count filter
    if (permissionFilter && permissionFilter !== 'all') {
      switch (permissionFilter) {
        case 'few':
          if (permissionCount > 5) return false;
          break;
        case 'medium':
          if (permissionCount <= 5 || permissionCount > 15) return false;
          break;
        case 'many':
          if (permissionCount <= 15) return false;
          break;
      }
    }

    return true;
  }) || [];

  // Apply pagination to filtered results
  const filteredRoles = paginateData(allFilteredRoles, currentPage, pageSize);
  const totalPages = calculateTotalPages(allFilteredRoles.length, pageSize);

  // Check if any filters are applied
  const hasActiveFilters = searchTerm || userCountFilter || permissionFilter;

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, userCountFilter, permissionFilter]);

  if (error) {
    return (
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="text-center text-destructive">
            加载失败：{error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl">角色管理</h1>
          <p className="text-muted-foreground mt-1">管理系统角色和权限配置</p>
        </div>
        <Button onClick={handleCreateRole} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          创建角色
        </Button>
      </div>

      {/* Roles list */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg">角色列表</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and filters */}
          <div className="space-y-4">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索角色名称、描述或权限..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
                <Select value={userCountFilter} onValueChange={handleUserCountFilter}>
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder="用户数量" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有角色</SelectItem>
                    <SelectItem value="none">无用户</SelectItem>
                    <SelectItem value="some">有用户</SelectItem>
                    <SelectItem value="many">用户较多 (≥5)</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={permissionFilter} onValueChange={handlePermissionFilter}>
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder="权限数量" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有权限</SelectItem>
                    <SelectItem value="few">权限较少 (≤5)</SelectItem>
                    <SelectItem value="medium">权限中等 (6-15)</SelectItem>
                    <SelectItem value="many">权限较多 (&gt;15)</SelectItem>
                  </SelectContent>
                </Select>

                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                    className="w-full sm:w-auto"
                  >
                    <X className="h-4 w-4 mr-1" />
                    清除筛选
                  </Button>
                )}
              </div>
            </div>

            {/* Filter indicators */}
            {hasActiveFilters && (
              <div className="flex flex-wrap gap-2">
                {searchTerm && (
                  <Badge variant="secondary" className="text-xs">
                    搜索: {searchTerm}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handleSearch('')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {userCountFilter && userCountFilter !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    用户: {
                      userCountFilter === 'none' ? '无用户' :
                      userCountFilter === 'some' ? '有用户' : '用户较多'
                    }
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handleUserCountFilter('all')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {permissionFilter && permissionFilter !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    权限: {
                      permissionFilter === 'few' ? '权限较少' :
                      permissionFilter === 'medium' ? '权限中等' : '权限较多'
                    }
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handlePermissionFilter('all')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </div>
            )}
          </div>
          {isLoading || !allPermissions ? (
            <div className="space-y-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Desktop table */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>角色名称</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>权限数量</TableHead>
                      <TableHead>用户数量</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRoles?.map((role) => {
                      const userCount = getRoleUserCount(role.id);
                      const permissionCount = getPermissionCount(role.permissions);
                      
                      return (
                        <TableRow key={role.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <Shield className="h-4 w-4 text-muted-foreground" />
                              {role.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-xs truncate">
                              {role.description}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Badge variant="secondary" className="cursor-help">
                                    {permissionCount} 个权限
                                  </Badge>
                                </TooltipTrigger>
                                <TooltipContent side="top" className="max-w-xs">
                                  <div className="space-y-1">
                                    <div className="font-medium">权限���表:</div>
                                    {role.permissions.split(',').slice(0, 10).map((perm, index) => (
                                      <div key={index} className="font-mono text-xs">
                                        {perm.trim()}
                                      </div>
                                    ))}
                                    {role.permissions.split(',').length > 10 && (
                                      <div className="text-xs text-muted-foreground">
                                        ...还有 {role.permissions.split(',').length - 10} 个权限
                                      </div>
                                    )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                              {role.permissions && (
                                <div className="text-xs text-muted-foreground max-w-[200px] truncate">
                                  {role.permissions.split(',').slice(0, 2).map(p => p.trim()).join(', ')}
                                  {role.permissions.split(',').length > 2 && '...'}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center gap-1 cursor-help">
                                  <Users className="h-4 w-4 text-muted-foreground" />
                                  <span>{userCount} 个用户</span>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top" className="max-w-xs">
                                <div className="space-y-1">
                                  {userCount > 0 ? (
                                    <>
                                      <div className="font-medium">使用此角色的用户:</div>
                                      {getRoleUsers(role.id).slice(0, 10).map((user) => (
                                        <div key={user.id} className="text-xs">
                                          {user.username} {user.real_name && `(${user.real_name})`}
                                        </div>
                                      ))}
                                      {getRoleUsers(role.id).length > 10 && (
                                        <div className="text-xs text-muted-foreground">
                                          ...还有 {getRoleUsers(role.id).length - 10} 个用户
                                        </div>
                                      )}
                                    </>
                                  ) : (
                                    <div className="text-xs">暂无用户使用此角色</div>
                                  )}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditRole(role)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteRole(role)}
                                disabled={userCount > 0}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile cards */}
              <div className="md:hidden space-y-4">
                {filteredRoles?.map((role) => {
                  const userCount = getRoleUserCount(role.id);
                  const permissionCount = getPermissionCount(role.permissions);
                  
                  return (
                    <Card key={role.id}>
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-start gap-3">
                            <Shield className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium">{role.name}</h3>
                              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                {role.description}
                              </p>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex flex-wrap gap-2">
                              <Badge variant="secondary">
                                {permissionCount} 个权限
                              </Badge>
                              <Badge variant="outline" className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                {userCount} 个用户
                              </Badge>
                            </div>
                            
                            {role.permissions && (
                              <div className="text-xs text-muted-foreground">
                                <div className="font-medium mb-1">权限包含:</div>
                                <div className="space-y-1">
                                  {role.permissions.split(',').slice(0, 3).map((perm, index) => (
                                    <div key={index} className="font-mono">
                                      {perm.trim()}
                                    </div>
                                  ))}
                                  {role.permissions.split(',').length > 3 && (
                                    <div className="text-muted-foreground">
                                      ...还有 {role.permissions.split(',').length - 3} 个权限
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2 pt-2 border-t">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditRole(role)}
                              className="flex-1"
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              编辑
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteRole(role)}
                              disabled={userCount > 0}
                              className="flex-1"
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              删除
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {allFilteredRoles.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  {hasActiveFilters ? '没有找到匹配的角色' : '暂无角色'}
                </div>
              )}


            </div>
          )}
        </CardContent>
        
        {/* Pagination */}
        {allFilteredRoles.length > 0 && (
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={allFilteredRoles.length}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}
      </Card>

      {/* Dialogs */}
      <RoleDialog 
        open={roleDialogOpen}
        onOpenChange={setRoleDialogOpen}
        role={selectedRole}
        onSuccess={() => mutate()}
      />

      <DeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="删除角色"
        description="确定要删除此角色吗？只有未被用户使用的角色才能删除。"
        onConfirm={confirmDelete}
      />
    </div>
  );
}