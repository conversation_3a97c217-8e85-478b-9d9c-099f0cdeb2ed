import { 
  User, 
  Role, 
  Permission, 
  CreateUserRequest, 
  UpdateUserRequest, 
  CreateRoleRequest, 
  UpdateRoleRequest,
  AssignRoleRequest,
  PaginatedResponse,
  PaginationParams
} from '../types/auth';

// Mock data for development
const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    real_name: '管理员',
    is_active: true,
    is_superuser: true,
    roles: [
      { id: 1, name: '超级管理员', description: '拥有所有权限', permissions: 'api:user:*:*,api:role:*:*,api:content:*:*' }
    ]
  },
  {
    id: 2,
    username: 'editor',
    real_name: '编辑员',
    is_active: true,
    is_superuser: false,
    roles: [
      { id: 2, name: '编辑员', description: '内容编辑权限', permissions: 'api:content:article:read,api:content:article:write.create,api:content:article:write.update' }
    ]
  },
  {
    id: 3,
    username: 'viewer',
    real_name: '查看员',
    is_active: true,
    is_superuser: false,
    roles: [
      { id: 3, name: '查看员', description: '只读权限', permissions: 'api:content:*:read' }
    ]
  }
];

const mockRoles: Role[] = [
  { id: 1, name: '超级管理员', description: '拥有所有权限', permissions: 'api:user:*:*,api:role:*:*,api:content:*:*' },
  { id: 2, name: '编辑员', description: '内容编辑权限', permissions: 'api:content:article:read,api:content:article:write.create,api:content:article:write.update' },
  { id: 3, name: '查看员', description: '只读权限', permissions: 'api:content:*:read' },
  { id: 4, name: '审核员', description: '内容审核权限', permissions: 'api:content:article:read,api:content:review:write' }
];

const mockPermissions: Permission[] = [
  { type: 'api', service: 'user', module: 'profile', action: 'read', name: '用户资料读取', description: '读取用户基本信息' },
  { type: 'api', service: 'user', module: 'profile', action: 'write', name: '用户资料编辑', description: '编辑用户基本信息' },
  { type: 'api', service: 'user', module: 'management', action: 'read', name: '用户管理查看', description: '查看用户管理界面' },
  { type: 'api', service: 'user', module: 'management', action: 'write.create', name: '用户创建', description: '创建新用户' },
  { type: 'api', service: 'user', module: 'management', action: 'write.update', name: '用户编辑', description: '编辑用户信息' },
  { type: 'api', service: 'user', module: 'management', action: 'write.delete', name: '用户删除', description: '删除用户' },
  { type: 'api', service: 'role', module: 'management', action: 'read', name: '角色管理查看', description: '查看角色管理界面' },
  { type: 'api', service: 'role', module: 'management', action: 'write.create', name: '角色创建', description: '创建新角色' },
  { type: 'api', service: 'role', module: 'management', action: 'write.update', name: '角色编辑', description: '编辑角色信息' },
  { type: 'api', service: 'role', module: 'management', action: 'write.delete', name: '角色删除', description: '删除角色' },
  { type: 'api', service: 'content', module: 'article', action: 'read', name: '文章读取', description: '读取文章内容' },
  { type: 'api', service: 'content', module: 'article', action: 'write.create', name: '文章创建', description: '创建新文章' },
  { type: 'api', service: 'content', module: 'article', action: 'write.update', name: '文章编辑', description: '编辑文章内容' },
  { type: 'api', service: 'content', module: 'article', action: 'write.delete', name: '文章删除', description: '删除文章' },
  { type: 'api', service: 'content', module: 'review', action: 'read', name: '审核查看', description: '查看待审核内容' },
  { type: 'api', service: 'content', module: 'review', action: 'write', name: '内容审核', description: '审核内容' }
];

// Utility function to simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Parse permission string to Permission objects
export const parsePermissions = (permissionString: string): Permission[] => {
  if (!permissionString) return [];
  
  return permissionString.split(',').map(perm => {
    const [type, service, module, action] = perm.trim().split(':');
    const existing = mockPermissions.find(p => 
      p.type === type && p.service === service && p.module === module && p.action === action
    );
    
    return existing || {
      type: type as any,
      service,
      module,
      action: action as any,
      name: `${service}.${module}.${action}`,
      description: `${service} ${module} ${action} permission`
    };
  });
};

// Convert Permission objects to permission string
export const stringifyPermissions = (permissions: Permission[]): string => {
  return permissions.map(p => `${p.type}:${p.service}:${p.module}:${p.action}`).join(',');
};

// Expand wildcard permissions to actual permission strings
export const expandWildcardPermissions = (permissionStrings: string[], allPermissions: Permission[]): string[] => {
  const expandedPermissions = new Set<string>();
  
  permissionStrings.forEach(permString => {
    const [type, service, module, action] = permString.split(':');
    
    if (!type || !service || !module || !action) return;
    
    if (action === '*' && module === '*') {
      // Service level wildcard: api:user:*:*
      allPermissions
        .filter(p => p.type === type && p.service === service)
        .forEach(p => expandedPermissions.add(`${p.type}:${p.service}:${p.module}:${p.action}`));
    } else if (action === '*') {
      // Module level wildcard: api:content:article:*
      allPermissions
        .filter(p => p.type === type && p.service === service && p.module === module)
        .forEach(p => expandedPermissions.add(`${p.type}:${p.service}:${p.module}:${p.action}`));
    } else if (module === '*') {
      // Service action wildcard: api:content:*:read
      allPermissions
        .filter(p => p.type === type && p.service === service && p.action === action)
        .forEach(p => expandedPermissions.add(`${p.type}:${p.service}:${p.module}:${p.action}`));
    } else {
      // Exact permission: api:user:profile:read
      const exists = allPermissions.some(p => 
        p.type === type && p.service === service && 
        p.module === module && p.action === action
      );
      if (exists) {
        expandedPermissions.add(permString);
      }
    }
  });
  
  return Array.from(expandedPermissions);
};

// Compress permissions to use wildcards where possible
export const compressPermissions = (permissionStrings: string[], allPermissions: Permission[]): string[] => {
  const compressed = new Set<string>();
  const processed = new Set<string>();
  
  // Group permissions by type:service
  const serviceGroups: { [key: string]: string[] } = {};
  
  permissionStrings.forEach(permString => {
    const [type, service, module, action] = permString.split(':');
    const serviceKey = `${type}:${service}`;
    
    if (!serviceGroups[serviceKey]) {
      serviceGroups[serviceKey] = [];
    }
    serviceGroups[serviceKey].push(permString);
  });
  
  // Check each service group for wildcards
  Object.entries(serviceGroups).forEach(([serviceKey, permissions]) => {
    const [type, service] = serviceKey.split(':');
    
    // Get all possible permissions for this service
    const allServicePermissions = allPermissions
      .filter(p => p.type === type && p.service === service)
      .map(p => `${p.type}:${p.service}:${p.module}:${p.action}`);
    
    // Check if we have all permissions for this service
    if (permissions.length === allServicePermissions.length && 
        allServicePermissions.every(p => permissions.includes(p))) {
      compressed.add(`${type}:${service}:*:*`);
      permissions.forEach(p => processed.add(p));
      return;
    }
    
    // Group by module within service
    const moduleGroups: { [key: string]: string[] } = {};
    permissions.forEach(permString => {
      const [, , module] = permString.split(':');
      const moduleKey = `${serviceKey}:${module}`;
      
      if (!moduleGroups[moduleKey]) {
        moduleGroups[moduleKey] = [];
      }
      moduleGroups[moduleKey].push(permString);
    });
    
    // Check each module group for wildcards
    Object.entries(moduleGroups).forEach(([moduleKey, modulePermissions]) => {
      const [, , module] = moduleKey.split(':');
      
      // Get all possible permissions for this module
      const allModulePermissions = allPermissions
        .filter(p => p.type === type && p.service === service && p.module === module)
        .map(p => `${p.type}:${p.service}:${p.module}:${p.action}`);
      
      if (modulePermissions.length === allModulePermissions.length &&
          allModulePermissions.every(p => modulePermissions.includes(p))) {
        compressed.add(`${type}:${service}:${module}:*`);
        modulePermissions.forEach(p => processed.add(p));
      } else {
        // Add individual permissions
        modulePermissions.forEach(p => {
          if (!processed.has(p)) {
            compressed.add(p);
            processed.add(p);
          }
        });
      }
    });
  });
  
  return Array.from(compressed);
};

// API functions
export const api = {
  // User APIs
  async getUsers(params?: PaginationParams): Promise<PaginatedResponse<User>> {
    await delay(500);
    
    let filteredUsers = [...mockUsers];
    
    // Text search
    if (params?.search) {
      const search = params.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.username.toLowerCase().includes(search) ||
        user.real_name?.toLowerCase().includes(search) ||
        user.roles.some(role => role.name.toLowerCase().includes(search))
      );
    }

    // Role filter
    if (params?.role && params.role !== 'all') {
      filteredUsers = filteredUsers.filter(user =>
        user.roles.some(role => role.name === params.role)
      );
    }

    // Status filter
    if (params?.status && params.status !== 'all') {
      filteredUsers = filteredUsers.filter(user => {
        switch (params.status) {
          case 'active':
            return user.is_active;
          case 'inactive':
            return !user.is_active;
          case 'superuser':
            return user.is_superuser;
          case 'regular':
            return !user.is_superuser;
          default:
            return true;
        }
      });
    }
    
    const page = params?.page || 1;
    const size = params?.size || 10;
    const start = (page - 1) * size;
    const end = start + size;
    
    return {
      items: filteredUsers.slice(start, end),
      total: filteredUsers.length,
      page,
      size,
      pages: Math.ceil(filteredUsers.length / size)
    };
  },

  async createUser(data: CreateUserRequest): Promise<User> {
    await delay(500);
    const newUser: User = {
      id: Date.now(),
      username: data.username,
      real_name: data.real_name || null,
      is_active: data.is_active,
      is_superuser: data.is_superuser,
      roles: []
    };
    mockUsers.push(newUser);
    return newUser;
  },

  async updateUser(data: UpdateUserRequest): Promise<User> {
    await delay(500);
    const index = mockUsers.findIndex(user => user.id === data.id);
    if (index === -1) throw new Error('用户不存在');
    
    mockUsers[index] = { ...mockUsers[index], ...data };
    return mockUsers[index];
  },

  async deleteUser(id: number): Promise<void> {
    await delay(500);
    const index = mockUsers.findIndex(user => user.id === id);
    if (index === -1) throw new Error('用户不存在');
    mockUsers.splice(index, 1);
  },

  async assignRoles(data: AssignRoleRequest): Promise<User> {
    await delay(500);
    const user = mockUsers.find(u => u.id === data.user_id);
    if (!user) throw new Error('用户不存在');
    
    const roles = mockRoles.filter(role => data.role_ids.includes(role.id));
    user.roles = roles;
    return user;
  },

  // Role APIs
  async getRoles(): Promise<Role[]> {
    await delay(500);
    return [...mockRoles];
  },

  async createRole(data: CreateRoleRequest): Promise<Role> {
    await delay(500);
    const newRole: Role = {
      id: Date.now(),
      name: data.name,
      description: data.description,
      permissions: data.permissions.join(',')
    };
    mockRoles.push(newRole);
    return newRole;
  },

  async updateRole(data: UpdateRoleRequest): Promise<Role> {
    await delay(500);
    const index = mockRoles.findIndex(role => role.id === data.id);
    if (index === -1) throw new Error('角色不存在');
    
    if (data.permissions) {
      data.permissions = data.permissions;
    }
    
    mockRoles[index] = { 
      ...mockRoles[index], 
      ...data,
      permissions: data.permissions ? data.permissions.join(',') : mockRoles[index].permissions
    };
    return mockRoles[index];
  },

  async deleteRole(id: number): Promise<void> {
    await delay(500);
    const index = mockRoles.findIndex(role => role.id === id);
    if (index === -1) throw new Error('角色不存在');
    
    // Check if any users have this role
    const usersWithRole = mockUsers.filter(user => 
      user.roles.some(role => role.id === id)
    );
    
    if (usersWithRole.length > 0) {
      throw new Error('无法删除角色：仍有用户使用此角色');
    }
    
    mockRoles.splice(index, 1);
  },

  // Permission APIs
  async getPermissions(): Promise<Permission[]> {
    await delay(300);
    return [...mockPermissions];
  }
};