export interface User {
  id: number;
  username: string;
  real_name?: string | null;
  lark_open_id?: string | null;
  is_active: boolean;
  is_superuser: boolean;
  roles: Role[];
}

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string; // comma separated permission strings
  users?: User[];
}

export interface UserRoleLink {
  user_id: number;
  role_id: number;
}

export type PermissionType = "api" | "field" | "record";
export type PermissionAction = "read" | "write" | "write.create" | "write.update" | "write.delete";

export interface Permission {
  type: PermissionType;
  service: string;
  module: string;
  action: PermissionAction;
  name?: string | null;
  description?: string | null;
}

export interface CreateUserRequest {
  username: string;
  real_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  password?: string;
}

export interface UpdateUserRequest {
  id: number;
  username?: string;
  real_name?: string;
  is_active?: boolean;
  is_superuser?: boolean;
}

export interface CreateRoleRequest {
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdateRoleRequest {
  id: number;
  name?: string;
  description?: string;
  permissions?: string[];
}

export interface AssignRoleRequest {
  user_id: number;
  role_ids: number[];
}

// Pagination types
export interface PaginationParams {
  page: number;
  size: number;
  search?: string;
  role?: string;
  status?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}