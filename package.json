{"name": "portal_dictionary", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/fs-routes": "^7.8.0", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "i": "^0.3.7", "immer": "^10.1.1", "input-otp": "^1.4.2", "isbot": "^5.1.27", "lodash": "^4.17.21", "lucide-react": "^0.539.0", "next-themes": "^0.4.6", "npm": "^11.5.2", "radix-ui": "^1.4.3", "react": "^19.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "react-router": "^7.7.1", "recharts": "^2.15.2", "sonner": "^2.0.3", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zustand": "^5.0.7"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}